package base

import (
	"byd_wallet/model"
	"context"

	"github.com/shopspring/decimal"
)

type TokenPriceReader interface {
	GetTokenLatestPriceUSDT(ctx context.Context, chainIndex int64, address string) (price decimal.Decimal, timeUnix int64, err error)
}

type GasPoolSponsorTxMgr interface {
	FindGasPoolSponsorTxByID(ctx context.Context, id uint) (*model.GasPoolSponsorTx, error)
	UpdateGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
}

type GasPoolMgr interface {
	DepositGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash, tokenAddress string) (*model.GasPoolFlow, error)
	RefundGasPool(ctx context.Context, reduceFlowID uint, amount decimal.Decimal) (*model.GasPoolFlow, error)
}

type GasPoolDepositAddressMgr interface {
	FindDepositReceiverAddress(ctx context.Context, chainIndex int64) (string, error)
}

// SolanaATAOwnerRepo Solana ATA Owner 数据库操作接口
// 定义了 ATA owner 映射关系的数据库操作方法
type SolanaATAOwnerRepo interface {
	// 基础查询操作
	GetByATAAddress(ctx context.Context, ataAddress string) (*model.SolanaATAOwner, error)
	BatchGetByATAAddresses(ctx context.Context, ataAddresses []string) ([]*model.SolanaATAOwner, error)
	ExistsByATAAddress(ctx context.Context, ataAddress string) (bool, error)

	// 创建和更新操作
	Create(ctx context.Context, record *model.SolanaATAOwner) error
	BatchUpsert(ctx context.Context, records []*model.SolanaATAOwner) error
	MarkAsVerified(ctx context.Context, ataAddress string) error
}
