package solana

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/model"
	"context"
	"encoding/binary"
	"fmt"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

// util.go - Solana paymaster 工具函数
// 包含通用验证函数、解析辅助函数、错误处理函数等

// ATA Owner 缓存相关常量
const (
	// DefaultATAOwnerCacheExpiration ATA owner 缓存默认过期时间（1小时）
	// ATA owner 关系相对稳定，可以设置较长的缓存时间
	DefaultATAOwnerCacheExpiration = time.Hour
)

// 通用验证辅助函数 - 用于减少重复的参数验证代码

// validateRawTx 验证原始交易数据的有效性
// 参数：rawTx - 待验证的原始交易数据
// 返回：error - 验证失败时的错误信息
func validateRawTx(rawTxHex string) error {
	if rawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// validateUserTx 验证用户交易数据的有效性
// 参数：tx - 待验证的用户交易数据
// 返回：error - 验证失败时的错误信息
func validateUserTx(tx *gaspool.UserTx) error {
	if tx == nil {
		return fmt.Errorf("tx is empty")
	}
	if tx.RawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// validateSponsorTx 验证赞助交易数据的有效性
// 参数：tx - 待验证的赞助交易数据
// 返回：error - 验证失败时的错误信息
func validateSponsorTx(tx *model.GasPoolSponsorTx) error {
	if tx == nil {
		return fmt.Errorf("paymaster tx is nil")
	}
	if tx.RawTxHex == "" {
		return fmt.Errorf("raw tx hex is empty")
	}
	return nil
}

// 交易解析辅助函数 - 用于减少重复的解析逻辑

// parseTransferAmount 解析转账金额
// 参数：
//   - data - 指令数据
//   - startOffset - 金额数据的起始偏移量
//
// 返回：uint64 - 解析出的转账金额
func parseTransferAmount(data []byte, startOffset int) (uint64, error) {
	if len(data) < startOffset+8 {
		return 0, fmt.Errorf("insufficient data for amount parsing")
	}
	return binary.LittleEndian.Uint64(data[startOffset : startOffset+8]), nil
}

// extractAccountAddresses 提取账户地址
// 参数：
//   - accountKeys - 账户公钥列表
//   - indices - 账户索引列表
//
// 返回：[]string - 对应的账户地址列表
func extractAccountAddresses(accountKeys []solana.PublicKey, indices []uint16) ([]string, error) {
	addresses := make([]string, len(indices))
	for i, index := range indices {
		if int(index) >= len(accountKeys) {
			return nil, fmt.Errorf("invalid account index: %d", index)
		}
		addresses[i] = accountKeys[index].String()
	}
	return addresses, nil
}

// validateInstructionData 验证指令数据的基本要求
// 参数：
//   - instruction - 编译后的指令
//   - minDataLen - 最小数据长度要求
//   - minAccountLen - 最小账户数量要求
//   - operation - 操作描述（用于错误信息）
//
// 返回：error - 验证失败时的错误信息
func validateInstructionData(instruction solana.CompiledInstruction, minDataLen, minAccountLen int, operation string) error {
	if len(instruction.Data) < minDataLen {
		return fmt.Errorf("insufficient %s instruction data length: %d, required: %d", operation, len(instruction.Data), minDataLen)
	}
	if len(instruction.Accounts) < minAccountLen {
		return fmt.Errorf("insufficient %s instruction accounts: %d, required: %d", operation, len(instruction.Accounts), minAccountLen)
	}
	return nil
}

// 错误处理辅助函数 - 用于统一错误消息格式和日志记录

// wrapError 包装错误并添加上下文信息
// 参数：
//   - operation - 操作描述
//   - err - 原始错误
//
// 返回：error - 包装后的错误
func wrapError(operation string, err error) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s failed: %w", operation, err)
}

// wrapErrorWithID 包装错误并添加交易ID上下文
// 参数：
//   - operation - 操作描述
//   - id - 交易ID
//   - err - 原始错误
//
// 返回：error - 包装后的错误
func wrapErrorWithID(operation string, id int64, err error) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s failed for transaction ID %d: %w", operation, id, err)
}

// 缓存管理辅助函数 - 用于优化密钥缓存逻辑

// initializeKeyCache 初始化密钥缓存
// 参数：
//   - pm - Paymaster实例
//   - ctx - 上下文
//
// 返回：error - 初始化失败时的错误信息
func (pm *Paymaster) initializeKeyCache(ctx context.Context) error {
	// 如果已缓存，直接返回
	if pm.cachedPrivateKey != nil && pm.cachedPublicKey != nil {
		return nil
	}

	// 获取私钥
	privateKeyStr, err := pm.hotAccountReader.GetHotAccount(ctx, constant.SolChainIndex)
	if err != nil {
		return wrapError("get hot account", err)
	}

	// 将Base58格式的私钥转换为Solana私钥对象
	solPrivateKey, err := solana.PrivateKeyFromBase58(privateKeyStr)
	if err != nil {
		return wrapError("decode private key", err)
	}

	// 缓存私钥和公钥
	pm.cachedPrivateKey = &solPrivateKey
	publicKey := solPrivateKey.PublicKey()
	pm.cachedPublicKey = &publicKey

	pm.log.Debugf("已缓存paymaster密钥对，公钥: %s", publicKey.String())
	return nil
}

// 交易处理辅助函数 - 用于优化交易准备和签名流程

// validateTransactionSignatures 验证交易签名的完整性
// 参数：
//   - tx - 待验证的交易
//   - expectedSignerCount - 期望的签名者数量
//
// 返回：error - 验证失败时的错误信息
func validateTransactionSignatures(tx *solana.Transaction, expectedSignerCount int) error {
	if len(tx.Signatures) != expectedSignerCount {
		return fmt.Errorf("签名数量不匹配: 期望 %d, 实际 %d", expectedSignerCount, len(tx.Signatures))
	}

	// 验证没有零值签名
	for i, sig := range tx.Signatures {
		if sig.IsZero() {
			return fmt.Errorf("签名索引 %d 为零值", i)
		}
	}

	return nil
}

// createPaymasterSignature 创建paymaster签名
// 参数：
//   - pm - Paymaster实例
//   - msgBytes - 待签名的消息字节
//
// 返回：
//   - solana.Signature - 生成的签名
//   - error - 签名失败时的错误信息
func (pm *Paymaster) createPaymasterSignature(msgBytes []byte) (solana.Signature, error) {
	// 获取paymaster私钥
	privateKey, err := pm.getPayPrivate()
	if err != nil {
		return solana.Signature{}, wrapError("get paymaster private key", err)
	}

	// 生成签名
	signature, err := privateKey.Sign(msgBytes)
	if err != nil {
		return solana.Signature{}, wrapError("sign message", err)
	}

	return signature, nil
}

// isPriceTimeExpired 检查价格时间是否过期
// 参数：
//   - timeUnix - 价格时间戳
//
// 返回：bool - 是否过期
func (pm *Paymaster) isPriceTimeExpired(timeUnix int64) bool {
	return time.Now().Unix() > timeUnix+pm.priceExpireSeconds
}

// verifyTxSignature 验证交易签名
// 参数：
//   - rawTxHex - 原始交易十六进制字符串
//
// 返回：
//   - bool - 签名是否有效
//   - error - 验证过程中的错误
func (pm *Paymaster) verifyTxSignature(rawTxHex string) (bool, error) {
	_, err := pm.prepareTransaction(rawTxHex)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (pm *Paymaster) decimals(token string) decimal.Decimal {
	if token != "" {
		return decimal.NewFromInt(6)
	}
	return decimal.NewFromInt(9)
}

type TokenAccountData struct {
	Mint   solana.PublicKey // 0-32
	Owner  solana.PublicKey // 32-64
	Amount uint64           // 64-72
	// 后续字段你可以忽略或添加
}

// GetOwner 获取 ATA (Associated Token Account) 对应的 owner 地址
// 实现三层查询策略：Redis 缓存 -> PostgreSQL 数据库 -> Solana RPC
// 参数：
//   - ata: ATA (Associated Token Account) 公钥
//
// 返回：
//   - solana.PublicKey: owner 公钥
//   - error: 查询过程中的错误
func (pm *Paymaster) GetOwner(ata solana.PublicKey) (solana.PublicKey, error) {
	ctx := context.Background()
	ataAddress := ata.String()

	// 第一层：尝试从 Redis 缓存中获取 owner 地址
	owner, found, err := pm.getOwnerFromCache(ctx, ata)
	if err != nil {
		// Redis 查询出错，记录警告但继续下一层查询
		pm.log.Warnf("从 Redis 缓存获取 ATA owner 失败: %s, 错误: %v", ataAddress, err)
	} else if found {
		// 缓存命中，直接返回结果
		pm.log.Debugf("ATA owner Redis 缓存命中: %s -> %s", ataAddress, owner.String())
		return owner, nil
	}

	// 第二层：Redis 缓存未命中，尝试从 PostgreSQL 数据库获取
	pm.log.Debugf("ATA owner Redis 缓存未命中，从数据库查询: %s", ataAddress)
	dbRecord, err := pm.ataOwnerRepo.GetByATAAddress(ctx, ataAddress)
	if err != nil {
		// 数据库查询出错，记录警告但继续下一层查询
		pm.log.Warnf("从数据库获取 ATA owner 失败: %s, 错误: %v", ataAddress, err)
	} else if dbRecord != nil && dbRecord.IsValid() {
		// 数据库命中，解析 owner 地址
		ownerFromDB, err := solana.PublicKeyFromBase58(dbRecord.OwnerAddress)
		if err != nil {
			pm.log.Errorf("解析数据库中的 owner 地址失败: %s, 错误: %v", dbRecord.OwnerAddress, err)
		} else {
			pm.log.Debugf("ATA owner 数据库命中: %s -> %s", ataAddress, ownerFromDB.String())

			// 异步将数据库结果同步到 Redis 缓存
			go func() {
				cacheCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
				defer cancel()

				if err := pm.setOwnerToCache(cacheCtx, ata, ownerFromDB, DefaultATAOwnerCacheExpiration); err != nil {
					pm.log.Warnf("同步数据库结果到 Redis 缓存失败: %s -> %s, 错误: %v", ataAddress, ownerFromDB.String(), err)
				} else {
					pm.log.Debugf("已同步数据库结果到 Redis 缓存: %s -> %s", ataAddress, ownerFromDB.String())
				}
			}()

			return ownerFromDB, nil
		}
	}

	// 第三层：数据库也未命中，从 Solana RPC 获取最新数据
	pm.log.Debugf("ATA owner 数据库未命中，从 RPC 获取: %s", ataAddress)
	var tokenAccountData TokenAccountData
	err = pm.solCli.Select().GetAccountDataInto(ctx, ata, &tokenAccountData)
	if err != nil {
		return ata, wrapError("get account info", err)
	}

	owner = tokenAccountData.Owner
	pm.log.Debugf("从 RPC 获取 ATA owner: %s -> %s", ataAddress, owner.String())

	// 异步将 RPC 结果持久化到数据库和缓存
	go func() {
		persistCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 保存到数据库
		if err := pm.persistOwnerToDatabase(persistCtx, ata, owner, "rpc"); err != nil {
			pm.log.Warnf("持久化 ATA owner 到数据库失败: %s -> %s, 错误: %v", ataAddress, owner.String(), err)
		}

		// 保存到 Redis 缓存
		if err := pm.setOwnerToCache(persistCtx, ata, owner, DefaultATAOwnerCacheExpiration); err != nil {
			pm.log.Warnf("缓存 ATA owner 到 Redis 失败: %s -> %s, 错误: %v", ataAddress, owner.String(), err)
		}
	}()

	return owner, nil
}

// getOwnerFromCache 从 Redis 缓存中获取 ATA 对应的 owner 地址
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//
// 返回：
//   - solana.PublicKey: owner 公钥，如果缓存未命中则返回空值
//   - bool: 是否在缓存中找到
//   - error: 查询过程中的错误
func (pm *Paymaster) getOwnerFromCache(ctx context.Context, ata solana.PublicKey) (solana.PublicKey, bool, error) {
	// 生成 Redis 缓存键
	cacheKey := common.GetSolOwner(ata.String())

	// 从 Redis 获取缓存的 owner 地址
	ownerStr, err := pm.rd.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中，返回 false 表示需要从 RPC 获取
			return solana.PublicKey{}, false, nil
		}
		// Redis 查询错误
		return solana.PublicKey{}, false, fmt.Errorf("redis 查询失败: %w", err)
	}

	// 解析 owner 地址字符串为公钥
	owner, err := solana.PublicKeyFromBase58(ownerStr)
	if err != nil {
		// 缓存数据损坏，删除无效缓存
		pm.rd.Del(ctx, cacheKey)
		return solana.PublicKey{}, false, fmt.Errorf("解析缓存的 owner 地址失败: %w", err)
	}

	return owner, true, nil
}

// setOwnerToCache 将 ATA 和对应的 owner 地址缓存到 Redis
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//   - owner: owner 公钥
//   - expiration: 缓存过期时间
//
// 返回：
//   - error: 缓存设置过程中的错误
func (pm *Paymaster) setOwnerToCache(ctx context.Context, ata solana.PublicKey, owner solana.PublicKey, expiration time.Duration) error {
	// 生成 Redis 缓存键
	cacheKey := common.GetSolOwner(ata.String())

	// 将 owner 地址缓存到 Redis
	err := pm.rd.Set(ctx, cacheKey, owner.String(), expiration).Err()
	if err != nil {
		return fmt.Errorf("redis 设置失败: %w", err)
	}

	pm.log.Debugf("ATA owner 已缓存: %s -> %s, 过期时间: %v", ata.String(), owner.String(), expiration)
	return nil
}

// persistOwnerToDatabase 将 ATA 和对应的 owner 地址持久化到数据库
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//   - owner: owner 公钥
//   - source: 数据来源标识
//
// 返回：
//   - error: 持久化过程中的错误
func (pm *Paymaster) persistOwnerToDatabase(ctx context.Context, ata solana.PublicKey, owner solana.PublicKey, source string) error {
	// 导入必要的包
	now := time.Now()

	// 创建数据库记录
	record := &model.SolanaATAOwner{
		ATAAddress:         ata.String(),
		OwnerAddress:       owner.String(),
		ChainIndex:         501, // Solana 主网链索引
		Source:             source,
		VerificationStatus: "verified",
		LastVerifiedAt:     &now,
	}

	// 使用 BatchUpsert 进行插入或更新操作
	err := pm.ataOwnerRepo.BatchUpsert(ctx, []*model.SolanaATAOwner{record})
	if err != nil {
		return fmt.Errorf("数据库操作失败: %w", err)
	}

	pm.log.Debugf("ATA owner 已持久化到数据库: %s -> %s, 来源: %s", ata.String(), owner.String(), source)
	return nil
}

// batchPersistOwnersToDatabase 批量将 ATA owner 映射持久化到数据库
// 参数：
//   - ctx: 上下文对象
//   - ataOwnerPairs: ATA 到 owner 的映射
//   - source: 数据来源标识
//
// 返回：
//   - error: 持久化过程中的错误
func (pm *Paymaster) batchPersistOwnersToDatabase(ctx context.Context, ataOwnerPairs map[solana.PublicKey]solana.PublicKey, source string) error {
	if len(ataOwnerPairs) == 0 {
		return nil
	}

	// 创建数据库记录列表
	records := make([]*model.SolanaATAOwner, 0, len(ataOwnerPairs))
	now := time.Now()

	for ataKey, ownerKey := range ataOwnerPairs {
		record := &model.SolanaATAOwner{
			ATAAddress:         ataKey.String(),
			OwnerAddress:       ownerKey.String(),
			ChainIndex:         501, // Solana 主网链索引
			Source:             source,
			VerificationStatus: "verified",
			LastVerifiedAt:     &now,
		}
		records = append(records, record)
	}

	// 批量插入或更新
	err := pm.ataOwnerRepo.BatchUpsert(ctx, records)
	if err != nil {
		return fmt.Errorf("批量数据库操作失败: %w", err)
	}

	pm.log.Debugf("批量 ATA owner 已持久化到数据库: %d 条记录, 来源: %s", len(records), source)
	return nil
}
