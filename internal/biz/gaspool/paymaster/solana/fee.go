package solana

import (
	"context"
	"errors"
	"fmt"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/shopspring/decimal"
)

// fee.go - Solana paymaster 费用计算相关功能
// 包含交易费用获取、租金费用计算等功能

// Solana 租金相关常量
const (
	// TokenAccountSize SPL Token 账户的标准大小（字节）
	TokenAccountSize = 165
	// SystemAccountMinSize 系统账户的最小大小（字节）
	SystemAccountMinSize = 0
	// MultisigAccountSize 多重签名账户的大小（字节）
	MultisigAccountSize = 355
	// MintAccountSize Mint 账户的标准大小（字节）
	MintAccountSize = 82
)

// Solana 程序 ID 常量
var (
	// AssociatedTokenAccountProgramID Associated Token Account 程序 ID
	// 用于创建和管理关联代币账户 (ATA)
	AssociatedTokenAccountProgramID = solana.MustPublicKeyFromBase58("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL")
)

// GetTransactionFeeWithContext 获取交易费用（带上下文）
// 参数：
//   - ctx: 上下文对象
//   - message: 交易消息字符串
//
// 返回：
//   - decimal.Decimal: 交易费用（以lamports为单位）
//   - error: 获取过程中的错误
func (pm *Paymaster) GetTransactionFeeWithContext(ctx context.Context, message string) (decimal.Decimal, error) {
	if message == "" {
		return decimal.Decimal{}, fmt.Errorf("message is empty")
	}

	pm.log.Debugf("开始获取交易费用，消息长度: %d", len(message))

	feeResp, err := pm.solCli.Select().GetFeeForMessage(ctx, message, rpc.CommitmentConfirmed)
	if err != nil {
		// 检查是否为 Solana blockhash 过期相关错误
		// Solana blockhash 机制：每个交易都包含一个 recent blockhash，用于防止重放攻击
		// blockhash 有效期约为 150 个区块（约 1-2 分钟），过期后交易将被拒绝
		if enhancedErr := pm.enhanceRPCError(err); enhancedErr != nil {
			return decimal.Decimal{}, enhancedErr
		}
		return decimal.Decimal{}, fmt.Errorf("get fee error: %w", err)
	}
	if feeResp == nil {
		return decimal.Decimal{}, errors.New("rpc response is nil")
	}
	if feeResp.Value == nil {
		return decimal.Decimal{}, errors.New("fee response is nil")
	}

	fee := decimal.NewFromInt(int64(*feeResp.Value))
	return fee, nil
}

// GetMinimumBalanceForRentExemption 获取账户免租金所需的最小余额
// 参数：
//   - ctx: 上下文对象
//   - accountSize: 账户大小（字节）
//
// 返回：
//   - decimal.Decimal: 免租金所需的最小余额（以lamports为单位）
//   - error: 获取过程中的错误
func (pm *Paymaster) GetMinimumBalanceForRentExemption(ctx context.Context, accountSize uint64) (decimal.Decimal, error) {
	pm.log.Debugf("开始获取账户免租金最小余额，账户大小: %d 字节", accountSize)

	// 调用 Solana RPC 获取免租金最小余额
	balanceResp, err := pm.solCli.Select().GetMinimumBalanceForRentExemption(ctx, accountSize, rpc.CommitmentFinalized)
	if err != nil {
		// 检查是否为特定的 RPC 错误，提供更友好的错误信息
		if enhancedErr := pm.enhanceRPCError(err); enhancedErr != nil {
			return decimal.Decimal{}, enhancedErr
		}
		return decimal.Decimal{}, fmt.Errorf("get minimum balance for rent exemption error: %w", err)
	}

	balance := decimal.NewFromInt(int64(balanceResp))
	pm.log.Debugf("账户大小 %d 字节的免租金最小余额: %s lamports", accountSize, balance.String())

	return balance, nil
}

// CalculateRentFeeForTransaction 计算交易中涉及的租金费用
// 参数：
//   - ctx: 上下文对象
//   - transaction: Solana 交易对象
//
// 返回：
//   - decimal.Decimal: 总租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) CalculateRentFeeForTransaction(ctx context.Context, transaction *solana.Transaction) (decimal.Decimal, error) {
	if transaction == nil {
		return decimal.Zero, fmt.Errorf("transaction is nil")
	}

	pm.log.Debugf("开始计算交易租金费用，指令数量: %d", len(transaction.Message.Instructions))

	totalRentFee := decimal.Zero

	// 遍历交易中的所有指令，检查是否有创建账户的操作
	for i, instruction := range transaction.Message.Instructions {
		rentFee, err := pm.calculateInstructionRentFee(ctx, instruction, transaction.Message.AccountKeys)
		if err != nil {
			pm.log.Warnf("计算指令 %d 租金费用失败: %v", i, err)
			continue // 继续处理其他指令，不因单个指令失败而中断
		}

		if rentFee.GreaterThan(decimal.Zero) {
			pm.log.Debugf("指令 %d 需要租金费用: %s lamports", i, rentFee.String())
			totalRentFee = totalRentFee.Add(rentFee)
		}
	}

	pm.log.Infof("交易总租金费用: %s lamports", totalRentFee.String())
	return totalRentFee, nil
}

// calculateInstructionRentFee 计算单个指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateInstructionRentFee(ctx context.Context, instruction solana.CompiledInstruction, accountKeys []solana.PublicKey) (decimal.Decimal, error) {
	// 检查指令是否为系统程序指令
	if int(instruction.ProgramIDIndex) >= len(accountKeys) {
		return decimal.Zero, fmt.Errorf("invalid program ID index: %d", instruction.ProgramIDIndex)
	}

	programID := accountKeys[instruction.ProgramIDIndex]

	// 处理系统程序指令（账户创建等）
	//if programID.Equals(solana.SystemProgramID) {
	//	return pm.calculateSystemInstructionRentFee(ctx, instruction, accountKeys)
	//}

	// 处理 SPL Token 程序指令
	if programID.Equals(solana.TokenProgramID) {
		return pm.calculateTokenInstructionRentFee(ctx, instruction, accountKeys)
	}

	// 处理 Associated Token Account 程序指令（ATA 创建）
	// 这是修复的关键部分：之前缺少对 ATA 程序的支持
	if programID.Equals(AssociatedTokenAccountProgramID) {
		return pm.calculateATAInstructionRentFee(ctx, instruction, accountKeys)
	}

	// 其他程序指令暂时不计算租金费用
	pm.log.Debugf("未知程序指令，程序ID: %s，跳过租金费用计算", programID.String())
	return decimal.Zero, nil
}

// calculateSystemInstructionRentFee 计算系统程序指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateSystemInstructionRentFee(ctx context.Context, instruction solana.CompiledInstruction, accountKeys []solana.PublicKey) (decimal.Decimal, error) {
	if len(instruction.Data) == 0 {
		return decimal.Zero, nil
	}

	// 解析系统指令类型
	instructionType := uint32(instruction.Data[0])
	switch instructionType {
	case system.Instruction_CreateAccount: // CreateAccount 指令
		return pm.calculateCreateAccountRentFee(ctx, instruction)
	case system.Instruction_CreateAccountWithSeed: // CreateAccountWithSeed 指令

		return pm.calculateCreateAccountWithSeedRentFee(ctx, instruction)
	default:
		// 其他系统指令不涉及账户创建，无需租金费用
		if instructionType == system.Instruction_Transfer {
			if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
				mintAccount := accountKeys[instruction.Accounts[0]]
				if exists, err := pm.checkAccountExists(ctx, mintAccount); err == nil && exists {
					pm.log.Debugf("Mint 账户已存在: %s，无需支付创建租金", mintAccount.String())
					return decimal.Zero, nil
				}
			}
			// Mint 账户大小为 82 字节
			return pm.GetMinimumBalanceForRentExemption(ctx, MintAccountSize)
		}
		return decimal.Zero, nil
	}
}

// calculateCreateAccountRentFee 计算 CreateAccount 指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateCreateAccountRentFee(ctx context.Context, instruction solana.CompiledInstruction) (decimal.Decimal, error) {
	// CreateAccount 指令数据结构：
	// [0] instruction_type (1 byte)
	// [1-8] lamports (8 bytes)
	// [9-16] space (8 bytes)
	// [17-48] owner (32 bytes)

	if len(instruction.Data) < 17 {
		return decimal.Zero, fmt.Errorf("CreateAccount instruction data too short: %d bytes", len(instruction.Data))
	}

	// 提取账户大小（space）
	spaceBytes := instruction.Data[9:17]
	space := uint64(0)
	for i := 0; i < 8; i++ {
		space |= uint64(spaceBytes[i]) << (8 * i)
	}

	pm.log.Debugf("CreateAccount 指令账户大小: %d 字节", space)

	// 验证账户大小的合理性
	if space > 10*1024*1024 { // 10MB 限制，防止异常大的值
		pm.log.Warnf("CreateAccount 指令解析出的账户大小异常大: %d 字节，可能存在解析错误", space)
		return decimal.Zero, fmt.Errorf("账户大小异常: %d 字节", space)
	}

	// 获取该大小账户的免租金最小余额
	return pm.GetMinimumBalanceForRentExemption(ctx, space)
}

// calculateCreateAccountWithSeedRentFee 计算 CreateAccountWithSeed 指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateCreateAccountWithSeedRentFee(ctx context.Context, instruction solana.CompiledInstruction) (decimal.Decimal, error) {
	// CreateAccountWithSeed 指令数据结构：
	// [0] instruction_type (1 byte)
	// [1-8] lamports (8 bytes)
	// [9-16] space (8 bytes)
	// [17-20] seed_length (4 bytes)
	// [21-21+seed_length] seed (variable length)
	// [21+seed_length-53+seed_length] owner (32 bytes)

	if len(instruction.Data) < 21 {
		pm.log.Warnf("CreateAccountWithSeed 指令数据长度不足: %d 字节，使用默认 Token 账户大小", len(instruction.Data))
		// 如果数据不足，使用标准 Token 账户大小作为后备方案
		return pm.GetMinimumBalanceForRentExemption(ctx, TokenAccountSize)
	}

	// 提取账户大小（space）- 位于字节 9-16
	spaceBytes := instruction.Data[9:17]
	space := uint64(0)
	for i := 0; i < 8; i++ {
		space |= uint64(spaceBytes[i]) << (8 * i)
	}

	pm.log.Debugf("CreateAccountWithSeed 指令解析出的账户大小: %d 字节", space)

	// 验证账户大小的合理性
	if space == 0 {
		pm.log.Warnf("CreateAccountWithSeed 指令解析出的账户大小为 0，使用默认 Token 账户大小")
		space = TokenAccountSize
	} else if space > 10*1024*1024 { // 10MB 限制，防止异常大的值
		pm.log.Warnf("CreateAccountWithSeed 指令解析出的账户大小异常大: %d 字节，使用默认 Token 账户大小", space)
		space = TokenAccountSize
	}

	// 获取该大小账户的免租金最小余额
	return pm.GetMinimumBalanceForRentExemption(ctx, space)
}

// calculateTokenInstructionRentFee 计算 SPL Token 程序指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateTokenInstructionRentFee(ctx context.Context, instruction solana.CompiledInstruction, accountKeys []solana.PublicKey) (decimal.Decimal, error) {
	if len(instruction.Data) == 0 {
		return decimal.Zero, nil
	}

	// 解析 SPL Token 指令类型
	instructionType := instruction.Data[0]

	switch instructionType {
	case 0: // InitializeMint 指令
		pm.log.Debugf("InitializeMint 指令，使用 Mint 账户大小")
		// 检查 Mint 账户是否已存在（通常是第一个账户）
		if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
			mintAccount := accountKeys[instruction.Accounts[0]]
			if exists, err := pm.checkAccountExists(ctx, mintAccount); err == nil && exists {
				pm.log.Debugf("Mint 账户已存在: %s，无需支付创建租金", mintAccount.String())
				return decimal.Zero, nil
			}
		}
		// Mint 账户大小为 82 字节
		return pm.GetMinimumBalanceForRentExemption(ctx, MintAccountSize)

	case 1: // InitializeAccount 指令
		pm.log.Debugf("InitializeAccount 指令，使用 Token 账户大小")
		// 检查 Token 账户是否已存在（通常是第一个账户）
		if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
			tokenAccount := accountKeys[instruction.Accounts[0]]
			if exists, err := pm.checkAccountExists(ctx, tokenAccount); err == nil && exists {
				pm.log.Debugf("Token 账户已存在: %s，无需支付创建租金", tokenAccount.String())
				return decimal.Zero, nil
			}
		}
		// Token 账户大小为 165 字节
		return pm.GetMinimumBalanceForRentExemption(ctx, TokenAccountSize)

	case 2: // InitializeMultisig 指令
		pm.log.Debugf("InitializeMultisig 指令，使用多重签名账户大小")
		// 检查多重签名账户是否已存在（通常是第一个账户）
		if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
			multisigAccount := accountKeys[instruction.Accounts[0]]
			if exists, err := pm.checkAccountExists(ctx, multisigAccount); err == nil && exists {
				pm.log.Debugf("多重签名账户已存在: %s，无需支付创建租金", multisigAccount.String())
				return decimal.Zero, nil
			}
		}
		// 多重签名账户大小为 355 字节
		return pm.GetMinimumBalanceForRentExemption(ctx, MultisigAccountSize)

	case 18: // InitializeAccount2 指令
		pm.log.Debugf("InitializeAccount2 指令，使用 Token 账户大小")
		// 检查 Token 账户是否已存在（通常是第一个账户）
		if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
			tokenAccount := accountKeys[instruction.Accounts[0]]
			if exists, err := pm.checkAccountExists(ctx, tokenAccount); err == nil && exists {
				pm.log.Debugf("Token 账户已存在: %s，无需支付创建租金", tokenAccount.String())
				return decimal.Zero, nil
			}
		}
		// Token 账户大小为 165 字节
		return pm.GetMinimumBalanceForRentExemption(ctx, TokenAccountSize)

	case 20: // InitializeAccount3 指令
		pm.log.Debugf("InitializeAccount3 指令，使用 Token 账户大小")
		// 检查 Token 账户是否已存在（通常是第一个账户）
		if len(instruction.Accounts) > 0 && int(instruction.Accounts[0]) < len(accountKeys) {
			tokenAccount := accountKeys[instruction.Accounts[0]]
			if exists, err := pm.checkAccountExists(ctx, tokenAccount); err == nil && exists {
				pm.log.Debugf("Token 账户已存在: %s，无需支付创建租金", tokenAccount.String())
				return decimal.Zero, nil
			}
		}
		// Token 账户大小为 165 字节
		return pm.GetMinimumBalanceForRentExemption(ctx, TokenAccountSize)

	default:
		// 其他 Token 指令（如 Transfer, Approve 等）不涉及账户创建，无需租金费用
		return decimal.Zero, nil
	}
}

// calculateATAInstructionRentFee 计算 Associated Token Account 程序指令的租金费用
// 参数：
//   - ctx: 上下文对象
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//
// 返回：
//   - decimal.Decimal: 指令的租金费用（以lamports为单位）
//   - error: 计算过程中的错误
func (pm *Paymaster) calculateATAInstructionRentFee(ctx context.Context, instruction solana.CompiledInstruction, accountKeys []solana.PublicKey) (decimal.Decimal, error) {
	// ATA 程序主要有一个指令：CreateAssociatedTokenAccount
	// 该指令会创建一个新的 Associated Token Account，大小固定为 165 字节

	pm.log.Debugf("检测到 ATA 创建指令，计算 ATA 账户租金费用")

	// 检查指令账户数量是否足够
	// CreateAssociatedTokenAccount 指令需要至少 7 个账户：
	// 0. 付费者 (payer)
	// 1. 关联代币账户地址 (associated token account)
	// 2. 所有者 (owner)
	// 3. 代币 mint 地址 (mint)
	// 4. 系统程序 (system program)
	// 5. 代币程序 (token program)
	// 6. 租金系统变量 (rent sysvar)
	if len(instruction.Accounts) < 7 {
		pm.log.Warnf("ATA 创建指令账户数量不足: %d，预期至少 7 个", len(instruction.Accounts))
		// 即使账户数量不足，我们仍然假设这是一个 ATA 创建指令
		// 因为这可能是指令格式的变化或者我们的理解有误
	}

	// 获取 ATA 地址（第二个账户）
	if len(instruction.Accounts) >= 2 {
		ataAccountIndex := instruction.Accounts[1]
		if int(ataAccountIndex) < len(accountKeys) {
			ataAddress := accountKeys[ataAccountIndex]

			// 检查 ATA 是否已经存在
			// 如果账户已存在，则不需要支付创建租金
			exists, err := pm.checkAccountExists(ctx, ataAddress)
			if err != nil {
				pm.log.Warnf("检查 ATA 账户是否存在失败: %s, 错误: %v，假设账户不存在", ataAddress.String(), err)
				// 如果检查失败，为了安全起见，假设账户不存在，需要支付租金
			} else if exists {
				pm.log.Debugf("ATA 账户已存在: %s，无需支付创建租金", ataAddress.String())
				return decimal.Zero, nil
			}

			pm.log.Debugf("ATA 账户不存在: %s，需要支付创建租金", ataAddress.String())
		}
	}

	// ATA 账户大小固定为 165 字节（与普通 Token 账户相同）
	return pm.GetMinimumBalanceForRentExemption(ctx, TokenAccountSize)
}

// checkAccountExists 检查指定账户是否存在于 Solana 网络中
// 参数：
//   - ctx: 上下文对象
//   - account: 要检查的账户公钥
//
// 返回：
//   - bool: 账户是否存在
//   - error: 检查过程中的错误
func (pm *Paymaster) checkAccountExists(ctx context.Context, account solana.PublicKey) (bool, error) {
	// 使用 GetAccountInfo RPC 调用检查账户是否存在
	// 如果账户不存在，RPC 会返回 null
	accountInfo, err := pm.solCli.Select().GetAccountInfo(ctx, account)
	if err != nil {
		// 检查是否为"账户不存在"的错误
		errStr := err.Error()
		if containsIgnoreCase(errStr, "not found") ||
			containsIgnoreCase(errStr, "invalid account") ||
			containsIgnoreCase(errStr, "null") {
			// 这些错误通常表示账户不存在
			pm.log.Debugf("账户不存在: %s", account.String())
			return false, nil
		}
		// 其他错误（如网络错误）需要返回
		return false, fmt.Errorf("检查账户存在性时发生错误: %w", err)
	}

	// 如果 accountInfo 为 nil 或 Value 为 nil，表示账户不存在
	if accountInfo == nil || accountInfo.Value == nil {
		pm.log.Debugf("账户不存在: %s", account.String())
		return false, nil
	}

	pm.log.Debugf("账户存在: %s，余额: %d lamports", account.String(), accountInfo.Value.Lamports)
	return true, nil
}

// enhanceRPCError 增强 RPC 错误信息，提供更友好的错误提示
// 参数：
//   - err: 原始 RPC 错误
//
// 返回：
//   - error: 增强后的错误信息，如果不是特定错误则返回 nil
func (pm *Paymaster) enhanceRPCError(err error) error {
	if err == nil {
		return nil
	}

	errStr := err.Error()

	// 检查是否为 RPC 参数无效错误（-32602）和 "index out of bounds" 消息
	// 这通常表示交易中的 blockhash 已过期或账户索引无效
	if containsIgnoreCase(errStr, "-32602") && containsIgnoreCase(errStr, "index out of bounds") {
		pm.log.Warnf("检测到 Solana blockhash 过期错误: %v", err)
		return fmt.Errorf("blockhash that is too old")
	}

	// 检查其他常见的 blockhash 相关错误
	if containsIgnoreCase(errStr, "blockhash not found") ||
		containsIgnoreCase(errStr, "invalid blockhash") ||
		containsIgnoreCase(errStr, "transaction uses a blockhash that is too old") {
		pm.log.Warnf("检测到 Solana blockhash 相关错误: %v", err)
		return fmt.Errorf("blockhash is invalid or expired")
	}

	// 检查交易消息格式错误
	if containsIgnoreCase(errStr, "invalid transaction message") {
		pm.log.Warnf("检测到 Solana 交易消息格式错误: %v", err)
		return fmt.Errorf("invalid transaction message")
	}

	// 检查账户不存在错误
	if containsIgnoreCase(errStr, "account not found") {
		pm.log.Warnf("检测到 Solana 账户不存在错误: %v", err)
		return fmt.Errorf("account not found")
	}

	// 对于其他错误，返回 nil 表示不需要特殊处理
	return nil
}

// containsIgnoreCase 不区分大小写的字符串包含检查
// 参数：
//   - s: 源字符串
//   - substr: 要查找的子字符串
//
// 返回：
//   - bool: 是否包含子字符串
func containsIgnoreCase(s, substr string) bool {
	// 手动实现不区分大小写的包含检查，避免导入 strings 包
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}

	// 转换为小写进行比较
	sLower := make([]byte, len(s))
	substrLower := make([]byte, len(substr))

	for i := 0; i < len(s); i++ {
		if s[i] >= 'A' && s[i] <= 'Z' {
			sLower[i] = s[i] + 32 // 转换为小写
		} else {
			sLower[i] = s[i]
		}
	}

	for i := 0; i < len(substr); i++ {
		if substr[i] >= 'A' && substr[i] <= 'Z' {
			substrLower[i] = substr[i] + 32 // 转换为小写
		} else {
			substrLower[i] = substr[i]
		}
	}

	// 查找子字符串
	for i := 0; i <= len(sLower)-len(substrLower); i++ {
		match := true
		for j := 0; j < len(substrLower); j++ {
			if sLower[i+j] != substrLower[j] {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}

	return false
}
