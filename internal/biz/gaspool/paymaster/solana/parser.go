package solana

import (
	"byd_wallet/common/constant"
	"fmt"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/programs/token"
)

// parser.go - Solana paymaster 消息解析相关功能
// 包含交易消息解析、指令解析等功能

// TransactionInfo 交易信息结构体
type TransactionInfo struct {
	From  string `json:"from"`  // 发送方地址列表
	To    string `json:"to"`    // 接收方地址列表
	Value uint64 `json:"value"` // 转账金额列表
	Type  string `json:"type"`  // 交易类型 (SOL/SPL)
	Mint  string `json:"mint"`  // 代币合约地址(仅SPL代币)
}

// parseMessage 解析消息内容，提取交易详情
// 参数：
//   - message: Solana交易消息对象
//
// 返回：
//   - []*TransactionInfo: 解析出的交易信息列表
//   - error: 解析过程中的错误
func (pm *Paymaster) parseMessage(message *solana.Message) (txList []*TransactionInfo, err error) {
	// 获取账户密钥列表
	accountKeys := message.AccountKeys

	pm.log.Debugf("开始解析交易消息，指令数量: %d，账户数量: %d", len(message.Instructions), len(accountKeys))

	// 遍历所有指令
	for i, instruction := range message.Instructions {
		// 获取程序ID
		if int(instruction.ProgramIDIndex) >= len(accountKeys) {
			pm.log.Warnf("指令 %d 的程序ID索引超出范围: %d >= %d", i, instruction.ProgramIDIndex, len(accountKeys))
			continue
		}
		programID := accountKeys[instruction.ProgramIDIndex]

		pm.log.Debugf("处理指令 %d，程序ID: %s", i, programID.String())

		// 为每个指令创建独立的交易信息对象
		txInfo := &TransactionInfo{}

		// 根据程序类型解析指令
		if programID.Equals(solana.SystemProgramID) {
			// 解析系统程序指令 (SOL转账、账户创建等)
			err := pm.parseSystemInstruction(instruction, accountKeys, txInfo)
			if err != nil {
				pm.log.Debugf("解析系统指令 %d 失败: %v，跳过", i, err)
				continue
			}
			// 只有当解析出有效的交易信息时才添加到列表
			if txInfo.From != "" && txInfo.To != "" && txInfo.Value > 0 {
				txList = append(txList, txInfo)
				pm.log.Debugf("成功解析系统指令 %d: %s -> %s, %d lamports", i, txInfo.From, txInfo.To, txInfo.Value)
			}
		} else if programID.Equals(solana.TokenProgramID) {
			// 解析SPL代币程序指令
			err := pm.parseTokenInstruction(instruction, accountKeys, txInfo)
			if err != nil {
				pm.log.Debugf("解析代币指令 %d 失败: %v，跳过", i, err)
				continue
			}
			// 只有当解析出有效的交易信息时才添加到列表
			if txInfo.From != "" && txInfo.To != "" && txInfo.Value > 0 {
				txList = append(txList, txInfo)
				pm.log.Debugf("成功解析代币指令 %d: %s -> %s, %d tokens", i, txInfo.From, txInfo.To, txInfo.Value)
			}
		} else if programID.Equals(AssociatedTokenAccountProgramID) {
			// 解析 ATA 程序指令
			pm.log.Debugf("检测到 ATA 程序指令 %d，程序ID: %s", i, programID.String())
			// ATA 创建指令通常不涉及直接的代币转账，但会产生租金费用
			// 这里我们记录但不添加到交易列表中，因为租金费用会在 EstimateGas 中单独计算
		} else {
			// 其他程序指令
			pm.log.Debugf("跳过未知程序指令 %d，程序ID: %s", i, programID.String())
		}
	}

	pm.log.Debugf("交易消息解析完成，共解析出 %d 个有效交易", len(txList))
	return txList, nil
}

// parseSystemInstruction 解析系统程序指令 (SOL转账和账户创建)
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseSystemInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 检查指令数据长度
	if len(instruction.Data) == 0 {
		return fmt.Errorf("system instruction data is empty")
	}

	// 检查指令类型
	instructionType := instruction.Data[0]

	switch instructionType {
	case uint8(system.Instruction_CreateAccount): // CreateAccount 指令 (类型 0)
		return pm.parseCreateAccountInstruction(instruction, accountKeys, txInfo)

	case uint8(system.Instruction_Transfer): // Transfer 指令 (类型 2)
		return pm.parseTransferInstruction(instruction, accountKeys, txInfo)

	case uint8(system.Instruction_CreateAccountWithSeed): // CreateAccountWithSeed 指令 (类型 3)
		return pm.parseCreateAccountWithSeedInstruction(instruction, accountKeys, txInfo)

	case uint8(system.Instruction_Assign): // Assign 指令 (类型 1)
		// Assign 指令通常用于将账户分配给程序，不涉及转账
		pm.log.Debugf("检测到 Assign 指令，指令类型: %d", instructionType)
		return nil

	case uint8(system.Instruction_Allocate): // Allocate 指令 (类型 8)
		// Allocate 指令用于为账户分配空间，不涉及转账
		pm.log.Debugf("检测到 Allocate 指令，指令类型: %d", instructionType)
		return nil

	default:
		// 对于其他系统指令，记录日志但不报错，因为可能有新的指令类型
		pm.log.Debugf("未知的系统指令类型: %d，跳过解析", instructionType)
		return nil
	}
}

// parseCreateAccountInstruction 解析 CreateAccount 指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseCreateAccountInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// CreateAccount 指令数据结构：
	// [0] instruction_type (1 byte)
	// [1-8] lamports (8 bytes) - 转账到新账户的 SOL 数量
	// [9-16] space (8 bytes) - 新账户的大小
	// [17-48] owner (32 bytes) - 新账户的所有者程序

	if len(instruction.Data) < 17 {
		return fmt.Errorf("CreateAccount instruction data too short: %d bytes", len(instruction.Data))
	}

	// 提取转账金额（lamports）
	lamportsBytes := instruction.Data[1:9]
	lamports := uint64(0)
	for i := 0; i < 8; i++ {
		lamports |= uint64(lamportsBytes[i]) << (8 * i)
	}

	// 提取账户地址
	if len(instruction.Accounts) < 2 {
		return fmt.Errorf("CreateAccount instruction requires at least 2 accounts")
	}

	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
	if err != nil {
		return fmt.Errorf("extract CreateAccount addresses: %w", err)
	}

	// 设置交易信息 - CreateAccount 本质上是一个 SOL 转账 + 账户创建
	txInfo.From = addresses[0] // 付费者账户
	txInfo.To = addresses[1]   // 新创建的账户
	txInfo.Value = lamports    // 转账的 SOL 数量
	txInfo.Type = constant.NativeTokenType

	pm.log.Debugf("解析 CreateAccount 指令：从 %s 到 %s，金额: %d lamports",
		txInfo.From, txInfo.To, txInfo.Value)

	return nil
}

// parseTransferInstruction 解析 Transfer 指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTransferInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证
	if err := validateInstructionData(instruction, 9, 2, "system transfer"); err != nil {
		return err
	}

	// 使用统一的金额解析函数
	amount, err := parseTransferAmount(instruction.Data, 4)
	if err != nil {
		return fmt.Errorf("parse system transfer amount: %w", err)
	}

	// 使用统一的地址提取函数
	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
	if err != nil {
		return fmt.Errorf("extract system transfer addresses: %w", err)
	}

	// 设置交易信息
	txInfo.From = addresses[0]
	txInfo.To = addresses[1]
	txInfo.Value = amount
	txInfo.Type = constant.NativeTokenType

	pm.log.Debugf("解析 Transfer 指令：从 %s 到 %s，金额: %d lamports",
		txInfo.From, txInfo.To, txInfo.Value)

	return nil
}

// parseCreateAccountWithSeedInstruction 解析 CreateAccountWithSeed 指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseCreateAccountWithSeedInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// CreateAccountWithSeed 指令数据结构更复杂，包含可变长度的 seed
	// [0] instruction_type (1 byte)
	// [1-8] lamports (8 bytes)
	// [9-16] space (8 bytes)
	// [17-20] seed_length (4 bytes)
	// [21-21+seed_length] seed (variable length)
	// [21+seed_length-53+seed_length] owner (32 bytes)

	if len(instruction.Data) < 21 {
		return fmt.Errorf("CreateAccountWithSeed instruction data too short: %d bytes", len(instruction.Data))
	}

	// 提取转账金额（lamports）
	lamportsBytes := instruction.Data[1:9]
	lamports := uint64(0)
	for i := 0; i < 8; i++ {
		lamports |= uint64(lamportsBytes[i]) << (8 * i)
	}

	// 提取账户地址
	if len(instruction.Accounts) < 2 {
		return fmt.Errorf("CreateAccountWithSeed instruction requires at least 2 accounts")
	}

	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
	if err != nil {
		return fmt.Errorf("extract CreateAccountWithSeed addresses: %w", err)
	}

	// 设置交易信息 - CreateAccountWithSeed 也是一个 SOL 转账 + 账户创建
	txInfo.From = addresses[0] // 付费者账户
	txInfo.To = addresses[1]   // 新创建的账户
	txInfo.Value = lamports    // 转账的 SOL 数量
	txInfo.Type = constant.NativeTokenType

	pm.log.Debugf("解析 CreateAccountWithSeed 指令：从 %s 到 %s，金额: %d lamports",
		txInfo.From, txInfo.To, txInfo.Value)

	return nil
}

// parseTokenInstruction 解析SPL代币程序指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenInstruction(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 手动解析SPL代币指令
	if len(instruction.Data) == 0 {
		return fmt.Errorf("token data is empty")
	}

	// 获取指令类型
	instructionType := instruction.Data[0]

	switch instructionType {
	case token.Instruction_Transfer: // Transfer指令
		return pm.parseTokenTransferManual(instruction, accountKeys, txInfo)
	case token.Instruction_TransferChecked: // TransferChecked指令
		return pm.parseTokenTransferCheckedManual(instruction, accountKeys, txInfo)
	default:
		return fmt.Errorf("invalid instruction type: %d", instructionType)
	}
}

// parseTokenTransferManual 手动解析SPL代币转账指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenTransferManual(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证
	if err := validateInstructionData(instruction, 9, 3, "token transfer"); err != nil {
		return err
	}

	// 使用统一的金额解析函数 (Transfer指令格式: [指令类型(1字节)] + [金额(8字节)])
	amount, err := parseTransferAmount(instruction.Data, 1)
	if err != nil {
		return fmt.Errorf("parse token transfer amount: %w", err)
	}

	// 使用统一的地址提取函数
	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1]})
	if err != nil {
		return fmt.Errorf("extract token transfer addresses: %w", err)
	}
	from, err := pm.GetOwner(solana.MustPublicKeyFromBase58(addresses[0]))
	if err != nil {
		return fmt.Errorf("parse token transfer checked addresses: %w", err)
	}

	to, err := pm.GetOwner(solana.MustPublicKeyFromBase58(addresses[1]))
	if err != nil {
		return fmt.Errorf("parse token transfer checked addresses: %w", err)
	}
	// 设置交易信息
	txInfo.From = from.String() // 源代币账户
	txInfo.To = to.String()     // 目标代币账户
	txInfo.Value = amount
	txInfo.Type = constant.SPLTokenType

	// 如果有mint账户信息，也添加进去
	if len(instruction.Accounts) > 2 {
		mintIndex := instruction.Accounts[2]
		if int(mintIndex) < len(accountKeys) {
			txInfo.Mint = accountKeys[mintIndex].String()
		}
	}

	return nil
}

// parseTokenTransferCheckedManual 手动解析SPL代币转账检查指令
// 参数：
//   - instruction: 编译后的指令
//   - accountKeys: 账户公钥列表
//   - txInfo: 交易信息结构体（用于填充解析结果）
//
// 返回：
//   - error: 解析过程中的错误
func (pm *Paymaster) parseTokenTransferCheckedManual(
	instruction solana.CompiledInstruction,
	accountKeys []solana.PublicKey,
	txInfo *TransactionInfo,
) error {
	// 使用统一的指令数据验证 (TransferChecked指令格式: [指令类型(1字节)] + [金额(8字节)] + [精度(1字节)])
	if err := validateInstructionData(instruction, 10, 4, "token transfer checked"); err != nil {
		return err
	}

	// 使用统一的金额解析函数
	amount, err := parseTransferAmount(instruction.Data, 1)
	if err != nil {
		return fmt.Errorf("parse token transfer checked amount: %w", err)
	}

	// 使用统一的地址提取函数
	addresses, err := extractAccountAddresses(accountKeys, []uint16{instruction.Accounts[0], instruction.Accounts[1], instruction.Accounts[2]})
	if err != nil {
		return fmt.Errorf("extract token transfer checked addresses: %w", err)
	}
	from, err := pm.GetOwner(solana.MustPublicKeyFromBase58(addresses[0]))
	if err != nil {
		return fmt.Errorf("parse token transfer checked addresses: %w", err)
	}

	to, err := pm.GetOwner(solana.MustPublicKeyFromBase58(addresses[2]))
	if err != nil {
		return fmt.Errorf("parse token transfer checked addresses: %w", err)
	}
	// 设置交易信息
	txInfo.From = from.String() // 源代币账户
	txInfo.To = to.String()     // 目标代币账户
	txInfo.Value = amount
	txInfo.Type = constant.SPLTokenType
	txInfo.Mint = addresses[1] // 代币合约

	return nil
}
