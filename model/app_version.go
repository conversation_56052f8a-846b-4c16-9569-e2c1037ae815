package model

import (
	"gorm.io/gorm"
)

// AppVersionReminderType 版本提示周期类型
type AppVersionReminderType int

const (
	// ReminderEveryOpen 每次打开APP
	ReminderEveryOpen AppVersionReminderType = iota + 1
	// ReminderDaily 每24小时1次
	ReminderDaily
	// ReminderWeekly 每7天提示1次
	ReminderWeekly
	// ReminderNever 永不提醒
	ReminderNever
)

// AppVersion app版本管理结构体
type AppVersion struct {
	BaseModelNoDeleted
	// 版本号
	Version string `gorm:"not null;index:,unique,composite:version_app_type;comment:版本号"`
	// APP类型 (ios/android)
	AppType string `gorm:"not null;index:,unique,composite:version_app_type;comment:APP类型"`
	// 下载地址
	DownloadURL string `gorm:"comment:下载地址"`
	// 官方下载地址
	OfficialDownloadURL string `gorm:"comment:官方下载地址"`
	// 强制更新
	ForceUpdate bool `gorm:"default:false;comment:是否强制更新"`
	// 版本提示周期 (1:每次打开APP 2:每24小时1次 3:每7天提示1次 4:永不提醒)
	ReminderType AppVersionReminderType `gorm:"comment:版本提示周期"`

	// 关联关系定义
	AppVersionI18Ns []*AppVersionI18N `gorm:"foreignKey:AppVersionID"`
}

// TableName 返回AppVersion表名
func (AppVersion) TableName() string {
	return "app_versions"
}

// BeforeSave 执行Save前，删除关联数据
func (a AppVersion) BeforeSave(tx *gorm.DB) error {
	return a.BeforeDelete(tx)
}

// BeforeDelete AppVersion删除前清理关联数据
func (a AppVersion) BeforeDelete(tx *gorm.DB) error {
	return tx.Delete(&AppVersionI18N{}, "app_version_id=?", a.ID).Error
}

// AppVersionI18N app版本多语言结构体
type AppVersionI18N struct {
	BaseModelNoDeleted
	AppVersionID uint   `gorm:"index;comment:app版本主键ID"`
	Language     string `gorm:"index;comment:语言代码(zh-CN,en-US,ja-JP,ko-KR)"`
	Description  string `gorm:"type:text;comment:版本描述"`

	// 关联关系定义
	AppVersion *AppVersion `gorm:"foreignKey:AppVersionID;references:ID"`
}

// TableName 返回AppVersionI18N表名
func (AppVersionI18N) TableName() string {
	return "app_version_i18n"
}

var AppVersionStub = &AppVersion{}
